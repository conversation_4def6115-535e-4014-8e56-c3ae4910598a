import { useEffect, useRef } from 'react';

const CabinetGallery = () => {
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const slideElements = entry.target.querySelectorAll('.cabinet-slide-in');
            slideElements.forEach((element, index) => {
              setTimeout(() => {
                element.classList.add('visible');
              }, index * 80);
            });
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const cabinets = [
    {
      id: 1,
      image: "https://readdy.ai/api/search-image?query=modern%20wardrobe%20with%20sliding%20doors%2C%20elegant%20design%2C%20built-in%20lighting%2C%20organized%20interior%2C%20neutral%20colors%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=8&orientation=portrait",
      title: "خزانة ملابس عصرية",
      description: "تصميم أنيق مع أبواب منزلقة وإضاءة داخلية",
      size: "w-80 h-96"
    },
    {
      id: 2,
      image: "https://readdy.ai/api/search-image?query=luxury%20walk-in%20closet%2C%20custom%20cabinetry%2C%20island%20storage%2C%20glass%20display%20cases%2C%20elegant%20lighting%2C%20organized%20shelving%2C%20professional%20photography%2C%20advertisement%20quality&width=800&height=600&seq=9&orientation=landscape",
      title: "غرفة ملابس فاخرة",
      description: "تصميم مخصص مع جزيرة تخزين وعرض زجاجي",
      size: "w-96 h-80 mt-16"
    },
    {
      id: 3,
      image: "https://readdy.ai/api/search-image?query=minimalist%20bookshelf%20design%2C%20clean%20lines%2C%20wooden%20shelves%2C%20modern%20storage%20solution%2C%20elegant%20display%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=10&orientation=portrait",
      title: "مكتبة بتصميم مينيمال",
      description: "رفوف خشبية أنيقة بخطوط بسيطة ونظيفة",
      size: "w-80 h-96"
    },
    {
      id: 4,
      image: "https://readdy.ai/api/search-image?query=built-in%20wall%20cabinet%20with%20hidden%20storage%2C%20custom%20design%2C%20modern%20finish%2C%20integrated%20with%20wall%2C%20elegant%20solution%2C%20professional%20photography%2C%20advertisement%20quality&width=800&height=600&seq=11&orientation=landscape",
      title: "خزانة جدارية مدمجة",
      description: "تصميم مخصص مع تخزين مخفي ومتكامل مع الجدار",
      size: "w-96 h-80 mt-16"
    },
    {
      id: 5,
      image: "https://readdy.ai/api/search-image?query=luxury%20TV%20cabinet%20with%20display%20shelves%2C%20elegant%20design%2C%20integrated%20lighting%2C%20media%20storage%2C%20modern%20finish%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=12&orientation=portrait",
      title: "وحدة تلفزيون فاخرة",
      description: "تصميم أنيق مع رفوف عرض وإضاءة متكاملة",
      size: "w-80 h-96"
    },
    {
      id: 6,
      image: "https://readdy.ai/api/search-image?query=custom%20office%20cabinets%20and%20shelving%2C%20workspace%20storage%20solution%2C%20elegant%20design%2C%20organized%20system%2C%20professional%20look%2C%20advertisement%20quality&width=800&height=600&seq=13&orientation=landscape",
      title: "خزائن مكتبية مخصصة",
      description: "حلول تخزين أنيقة لمساحات العمل المكتبية",
      size: "w-96 h-80 mt-16"
    }
  ];

  return (
    <section id="cabinets" className="py-20 bg-white" ref={sectionRef}>
      <div className="container mx-auto px-6">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-4">
          معرض الخزائن
        </h2>
        <p className="text-gray-600 text-center max-w-2xl mx-auto mb-16">
          تصاميم خزائن عصرية وأنيقة تجمع بين الجمال والوظيفة لتلبية احتياجات التخزين مع الحفاظ على جمالية المكان
        </p>

        <div className="cabinet-scroll-container">
          <div className="horizontal-scroll pb-6">
            <div className="flex space-x-8 rtl:space-x-reverse" style={{ width: 'max-content', padding: '0 60px' }}>
              {cabinets.map((cabinet) => (
                <div
                  key={cabinet.id}
                  className={`cabinet-card cabinet-slide-in shimmer-effect ${cabinet.size} flex-shrink-0`}
                >
                  <img
                    src={cabinet.image}
                    alt={cabinet.title}
                    className="w-full h-full object-cover image-hover"
                  />
                  <div className="image-overlay">
                    <h3>{cabinet.title}</h3>
                    <p>{cabinet.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="text-center mt-12">
          <button className="bg-primary hover:bg-blue-600 text-white px-8 py-3 rounded-button font-medium transition-all duration-300 transform hover:scale-105 whitespace-nowrap">
            استكشف المزيد من الخزائن
          </button>
        </div>
      </div>
    </section>
  );
};

export default CabinetGallery;
