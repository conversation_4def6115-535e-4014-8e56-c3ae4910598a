import { useRef, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, EffectCoverflow, Autoplay, Thumbs } from 'swiper/modules';
import { motion, useInView, AnimatePresence } from 'framer-motion';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-coverflow';
import 'swiper/css/thumbs';

const KitchenGallery = () => {
  const sectionRef = useRef(null);
  const [thumbsSwiper, setThumbsSwiper] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [lightboxImage, setLightboxImage] = useState(null);
  const isInView = useInView(sectionRef, { once: true, threshold: 0.1 });

  const categories = [
    { id: 'all', name: 'جميع التصاميم', icon: 'ri-apps-line' },
    { id: 'modern', name: 'عصري', icon: 'ri-building-line' },
    { id: 'classic', name: 'كلاسيكي', icon: 'ri-home-heart-line' },
    { id: 'luxury', name: 'فاخر', icon: 'ri-vip-crown-line' },
    { id: 'minimal', name: 'مينيمال', icon: 'ri-layout-line' }
  ];

  const kitchens = [
    {
      id: 1,
      image: "https://readdy.ai/api/search-image?query=elegant%20modern%20kitchen%20with%20island%2C%20white%20cabinets%2C%20marble%20countertops%2C%20pendant%20lights%2C%20wooden%20accents%2C%20spacious%20design%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=2&orientation=portrait",
      title: "مطبخ مودرن أبيض",
      description: "تصميم عصري مع جزيرة مركزية ورخام فاخر",
      category: 'modern',
      features: ['جزيرة مركزية', 'رخام طبيعي', 'إضاءة LED'],
      price: 'من 25,000 ريال'
    },
    {
      id: 2,
      image: "https://readdy.ai/api/search-image?query=luxury%20dark%20kitchen%20with%20gold%20accents%2C%20black%20cabinets%2C%20marble%20countertops%2C%20modern%20appliances%2C%20elegant%20lighting%2C%20professional%20photography%2C%20advertisement%20quality&width=800&height=600&seq=3&orientation=landscape",
      title: "مطبخ فاخر داكن",
      description: "تصميم أنيق مع لمسات ذهبية وأجهزة حديثة",
      category: 'luxury',
      features: ['لمسات ذهبية', 'أجهزة ذكية', 'تشطيبات فاخرة'],
      price: 'من 45,000 ريال'
    },
    {
      id: 3,
      image: "https://readdy.ai/api/search-image?query=rustic%20kitchen%20with%20wooden%20cabinets%2C%20stone%20countertops%2C%20farmhouse%20sink%2C%20warm%20lighting%2C%20cozy%20atmosphere%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=4&orientation=portrait",
      title: "مطبخ ريفي خشبي",
      description: "تصميم دافئ مع أسطح حجرية وإضاءة هادئة",
      category: 'classic',
      features: ['خشب طبيعي', 'أسطح حجرية', 'طابع ريفي'],
      price: 'من 20,000 ريال'
    },
    {
      id: 4,
      image: "https://readdy.ai/api/search-image?query=minimalist%20kitchen%20with%20clean%20lines%2C%20white%20and%20wood%20combination%2C%20hidden%20storage%2C%20sleek%20appliances%2C%20natural%20light%2C%20professional%20photography%2C%20advertisement%20quality&width=800&height=600&seq=5&orientation=landscape",
      title: "مطبخ مينيمال",
      description: "تصميم بسيط وأنيق مع تخزين مخفي وخطوط نظيفة",
      category: 'minimal',
      features: ['تخزين مخفي', 'خطوط نظيفة', 'تصميم بسيط'],
      price: 'من 18,000 ريال'
    },
    {
      id: 5,
      image: "https://readdy.ai/api/search-image?query=contemporary%20kitchen%20with%20blue%20cabinets%2C%20quartz%20countertops%2C%20open%20shelving%2C%20modern%20lighting%2C%20stylish%20backsplash%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=6&orientation=portrait",
      title: "مطبخ معاصر أزرق",
      description: "تصميم عصري مع أرفف مفتوحة وإضاءة حديثة",
      category: 'modern',
      features: ['أرفف مفتوحة', 'إضاءة حديثة', 'ألوان جريئة'],
      price: 'من 30,000 ريال'
    },
    {
      id: 6,
      image: "https://readdy.ai/api/search-image?query=industrial%20style%20kitchen%20with%20concrete%20countertops%2C%20metal%20accents%2C%20exposed%20brick%2C%20pendant%20lights%2C%20professional%20appliances%2C%20professional%20photography%2C%20advertisement%20quality&width=800&height=600&seq=7&orientation=landscape",
      title: "مطبخ بطابع صناعي",
      description: "تصميم عصري مع أسطح خرسانية ولمسات معدنية",
      category: 'modern',
      features: ['طابع صناعي', 'أسطح خرسانية', 'لمسات معدنية'],
      price: 'من 35,000 ريال'
    }
  ];

  const filteredKitchens = selectedCategory === 'all'
    ? kitchens
    : kitchens.filter(kitchen => kitchen.category === selectedCategory);

  return (
    <section id="kitchens" className="relative py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50 overflow-hidden" ref={sectionRef}>
      {/* Background Decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200/20 to-purple-200/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-pink-200/20 to-orange-200/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl mb-6 shadow-lg">
            <i className="ri-restaurant-line text-2xl text-white"></i>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 bg-clip-text text-transparent mb-6">
            معرض
            <span className="block bg-gradient-to-r from-orange-600 via-red-600 to-pink-600 bg-clip-text text-transparent">
              المطابخ الفاخرة
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            استكشف مجموعتنا المتنوعة من تصاميم المطابخ العصرية والكلاسيكية التي تناسب مختلف الأذواق والمساحات
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mt-8 rounded-full"></div>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {categories.map((category, index) => (
            <motion.button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`group relative px-6 py-3 rounded-2xl font-medium transition-all duration-300 ${
                selectedCategory === category.id
                  ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg scale-105'
                  : 'bg-white/80 backdrop-blur-sm text-gray-700 hover:bg-white hover:scale-105 border border-gray-200'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <i className={`${category.icon} text-lg`}></i>
                <span>{category.name}</span>
              </div>
              {selectedCategory !== category.id && (
                <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
              )}
            </motion.button>
          ))}
        </motion.div>

        {/* Main Swiper Gallery */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mb-8"
        >
          <Swiper
            modules={[Navigation, Pagination, EffectCoverflow, Autoplay, Thumbs]}
            spaceBetween={30}
            slidesPerView={1}
            centeredSlides={true}
            effect="coverflow"
            coverflowEffect={{
              rotate: 50,
              stretch: 0,
              depth: 100,
              modifier: 1,
              slideShadows: true,
            }}
            autoplay={{
              delay: 4000,
              disableOnInteraction: false,
            }}
            navigation={{
              nextEl: '.swiper-button-next-custom',
              prevEl: '.swiper-button-prev-custom',
            }}
            pagination={{
              clickable: true,
              dynamicBullets: true,
            }}
            thumbs={{ swiper: thumbsSwiper }}
            breakpoints={{
              640: {
                slidesPerView: 1,
                spaceBetween: 20,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 30,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 40,
              },
            }}
            className="kitchen-swiper"
          >
            <AnimatePresence mode="wait">
              {filteredKitchens.map((kitchen, index) => (
                <SwiperSlide key={kitchen.id}>
                  <motion.div
                    className="group relative bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer"
                    whileHover={{ y: -10 }}
                    onClick={() => setLightboxImage(kitchen)}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    {/* Image Container */}
                    <div className="relative h-80 md:h-96 overflow-hidden">
                      <motion.img
                        src={kitchen.image}
                        alt={kitchen.title}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                        whileHover={{ scale: 1.1 }}
                      />

                      {/* Gradient Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                      {/* Price Badge */}
                      <div className="absolute top-4 right-4 bg-gradient-to-r from-orange-500 to-red-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                        {kitchen.price}
                      </div>

                      {/* Category Badge */}
                      <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-xs font-medium">
                        {categories.find(cat => cat.id === kitchen.category)?.name}
                      </div>

                      {/* Zoom Icon */}
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                        <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30">
                          <i className="ri-zoom-in-line text-2xl text-white"></i>
                        </div>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-orange-600 transition-colors duration-300">
                        {kitchen.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                        {kitchen.description}
                      </p>

                      {/* Features */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {kitchen.features.map((feature, idx) => (
                          <span
                            key={idx}
                            className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full font-medium"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>

                      {/* Action Button */}
                      <motion.button
                        className="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                          <i className="ri-eye-line"></i>
                          <span>عرض التفاصيل</span>
                        </div>
                      </motion.button>
                    </div>
                  </motion.div>
                </SwiperSlide>
              ))}
            </AnimatePresence>
          </Swiper>

          {/* Custom Navigation Buttons */}
          <div className="flex justify-center items-center space-x-4 rtl:space-x-reverse mt-8">
            <button className="swiper-button-prev-custom w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 border border-gray-200">
              <i className="ri-arrow-right-line text-xl text-gray-700"></i>
            </button>
            <button className="swiper-button-next-custom w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 border border-gray-200">
              <i className="ri-arrow-left-line text-xl text-gray-700"></i>
            </button>
          </div>
        </motion.div>

        {/* Thumbnails Swiper */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mb-12"
        >
          <Swiper
            onSwiper={setThumbsSwiper}
            spaceBetween={10}
            slidesPerView={4}
            freeMode={true}
            watchSlidesProgress={true}
            breakpoints={{
              640: {
                slidesPerView: 5,
                spaceBetween: 15,
              },
              768: {
                slidesPerView: 6,
                spaceBetween: 20,
              },
              1024: {
                slidesPerView: 8,
                spaceBetween: 20,
              },
            }}
            className="thumbs-swiper"
          >
            {filteredKitchens.map((kitchen) => (
              <SwiperSlide key={`thumb-${kitchen.id}`}>
                <div className="relative h-20 md:h-24 rounded-xl overflow-hidden cursor-pointer group border-2 border-transparent hover:border-orange-500 transition-all duration-300">
                  <img
                    src={kitchen.image}
                    alt={kitchen.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/0 transition-colors duration-300"></div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          {[
            { number: '500+', label: 'مطبخ منجز', icon: 'ri-restaurant-line', color: 'from-blue-500 to-cyan-500' },
            { number: '15+', label: 'سنة خبرة', icon: 'ri-award-line', color: 'from-green-500 to-emerald-500' },
            { number: '98%', label: 'رضا العملاء', icon: 'ri-heart-line', color: 'from-pink-500 to-rose-500' },
            { number: '24/7', label: 'دعم فني', icon: 'ri-customer-service-line', color: 'from-purple-500 to-violet-500' }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/50 hover:bg-white hover:scale-105 transition-all duration-300 shadow-lg"
              whileHover={{ y: -5 }}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
            >
              <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center shadow-lg`}>
                <i className={`${stat.icon} text-2xl text-white`}></i>
              </div>
              <div className={`text-3xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent mb-2`}>
                {stat.number}
              </div>
              <p className="text-gray-600 font-medium">{stat.label}</p>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Button */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 1 }}
        >
          <motion.button
            className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-12 py-4 rounded-2xl font-bold text-lg shadow-xl hover:shadow-2xl transition-all duration-300"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <i className="ri-phone-line text-xl"></i>
              <span>احجز استشارة مجانية</span>
              <i className="ri-arrow-left-line text-xl"></i>
            </div>
          </motion.button>
        </motion.div>
      </div>

      {/* Lightbox Modal */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setLightboxImage(null)}
          >
            <motion.div
              className="relative max-w-4xl w-full bg-white rounded-3xl overflow-hidden shadow-2xl"
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                onClick={() => setLightboxImage(null)}
                className="absolute top-4 right-4 z-10 w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors duration-300"
              >
                <i className="ri-close-line text-xl"></i>
              </button>

              <div className="grid md:grid-cols-2 gap-0">
                {/* Image */}
                <div className="relative h-64 md:h-96">
                  <img
                    src={lightboxImage.image}
                    alt={lightboxImage.title}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Content */}
                <div className="p-8">
                  <h3 className="text-3xl font-bold text-gray-800 mb-4">
                    {lightboxImage.title}
                  </h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {lightboxImage.description}
                  </p>

                  <div className="mb-6">
                    <h4 className="text-lg font-bold text-gray-800 mb-3">المميزات:</h4>
                    <div className="flex flex-wrap gap-2">
                      {lightboxImage.features.map((feature, idx) => (
                        <span
                          key={idx}
                          className="px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white text-sm rounded-full font-medium"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold text-orange-600">
                      {lightboxImage.price}
                    </div>
                    <button className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300">
                      طلب عرض سعر
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default KitchenGallery;
