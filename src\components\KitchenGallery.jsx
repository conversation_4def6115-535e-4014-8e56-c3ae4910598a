import { useEffect, useRef } from 'react';

const KitchenGallery = () => {
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const staggerElements = entry.target.querySelectorAll('.stagger-animation');
            staggerElements.forEach((element, index) => {
              setTimeout(() => {
                element.classList.add('animate');
              }, index * 100);
            });
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const kitchens = [
    {
      id: 1,
      image: "https://readdy.ai/api/search-image?query=elegant%20modern%20kitchen%20with%20island%2C%20white%20cabinets%2C%20marble%20countertops%2C%20pendant%20lights%2C%20wooden%20accents%2C%20spacious%20design%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=2&orientation=portrait",
      title: "مطبخ مودرن أبيض",
      description: "تصميم عصري مع جزيرة مركزية ورخام فاخر"
    },
    {
      id: 2,
      image: "https://readdy.ai/api/search-image?query=luxury%20dark%20kitchen%20with%20gold%20accents%2C%20black%20cabinets%2C%20marble%20countertops%2C%20modern%20appliances%2C%20elegant%20lighting%2C%20professional%20photography%2C%20advertisement%20quality&width=800&height=600&seq=3&orientation=landscape",
      title: "مطبخ فاخر داكن",
      description: "تصميم أنيق مع لمسات ذهبية وأجهزة حديثة"
    },
    {
      id: 3,
      image: "https://readdy.ai/api/search-image?query=rustic%20kitchen%20with%20wooden%20cabinets%2C%20stone%20countertops%2C%20farmhouse%20sink%2C%20warm%20lighting%2C%20cozy%20atmosphere%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=4&orientation=portrait",
      title: "مطبخ ريفي خشبي",
      description: "تصميم دافئ مع أسطح حجرية وإضاءة هادئة"
    },
    {
      id: 4,
      image: "https://readdy.ai/api/search-image?query=minimalist%20kitchen%20with%20clean%20lines%2C%20white%20and%20wood%20combination%2C%20hidden%20storage%2C%20sleek%20appliances%2C%20natural%20light%2C%20professional%20photography%2C%20advertisement%20quality&width=800&height=600&seq=5&orientation=landscape",
      title: "مطبخ مينيمال",
      description: "تصميم بسيط وأنيق مع تخزين مخفي وخطوط نظيفة"
    },
    {
      id: 5,
      image: "https://readdy.ai/api/search-image?query=contemporary%20kitchen%20with%20blue%20cabinets%2C%20quartz%20countertops%2C%20open%20shelving%2C%20modern%20lighting%2C%20stylish%20backsplash%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=6&orientation=portrait",
      title: "مطبخ معاصر أزرق",
      description: "تصميم عصري مع أرفف مفتوحة وإضاءة حديثة"
    },
    {
      id: 6,
      image: "https://readdy.ai/api/search-image?query=industrial%20style%20kitchen%20with%20concrete%20countertops%2C%20metal%20accents%2C%20exposed%20brick%2C%20pendant%20lights%2C%20professional%20appliances%2C%20professional%20photography%2C%20advertisement%20quality&width=800&height=600&seq=7&orientation=landscape",
      title: "مطبخ بطابع صناعي",
      description: "تصميم عصري مع أسطح خرسانية ولمسات معدنية"
    }
  ];

  return (
    <section id="kitchens" className="py-20 bg-gray-50" ref={sectionRef}>
      <div className="container mx-auto px-6">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-4">
          معرض المطابخ
        </h2>
        <p className="text-gray-600 text-center max-w-2xl mx-auto mb-16">
          استكشف مجموعتنا المتنوعة من تصاميم المطابخ العصرية والكلاسيكية التي تناسب مختلف الأذواق والمساحات
        </p>

        <div className="masonry-grid">
          {kitchens.map((kitchen, index) => (
            <div
              key={kitchen.id}
              className="item kitchen-card stagger-animation kitchen-hover-effect shimmer-effect"
            >
              <img
                src={kitchen.image}
                alt={kitchen.title}
                className="w-full h-full object-cover image-hover"
              />
              <div className="image-overlay">
                <h3>{kitchen.title}</h3>
                <p>{kitchen.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <button className="bg-primary hover:bg-blue-600 text-white px-8 py-3 rounded-button font-medium transition-all duration-300 transform hover:scale-105 whitespace-nowrap">
            عرض المزيد من التصاميم
          </button>
        </div>
      </div>
    </section>
  );
};

export default KitchenGallery;
